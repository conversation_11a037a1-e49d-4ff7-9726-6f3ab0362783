#!/usr/bin/env bun

/**
 * CLI script to update all currency exchange rates
 *
 * This script fetches the latest exchange rates for all supported currencies
 * and stores them in the database using batch operations.
 *
 * Usage:
 *   bun run scripts/update-currency-rates.ts
 *
 * Environment variables:
 *   - DATABASE_URL: PostgreSQL connection string
 *   - CURRENCYAPICOM_KEY: API key for CurrencyAPI.com
 *   - LOG_LEVEL: Logging level (debug, info, warn, error)
 */
import { updateAllRates } from "~/features/currencies/actions";
import logger from "~/lib/logger";

async function main() {
  const startTime = Date.now();

  logger.info("🚀 Starting currency rates update process");

  try {
    await updateAllRates();

    const duration = Date.now() - startTime;
    logger.info(
      { durationMs: duration, durationSeconds: Math.round(duration / 1000) },
      "✅ Currency rates update completed successfully"
    );

    process.exit(0);
  } catch (error) {
    const duration = Date.now() - startTime;
    logger.error(
      {
        error: error instanceof Error ? error.message : error,
        stack: error instanceof Error ? error.stack : undefined,
        durationMs: duration,
      },
      "❌ Currency rates update failed"
    );

    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on("unhandledRejection", (reason, promise) => {
  logger.error({ reason, promise }, "Unhandled promise rejection");
  process.exit(1);
});

// Handle uncaught exceptions
process.on("uncaughtException", (error) => {
  logger.error({ error: error.message, stack: error.stack }, "Uncaught exception");
  process.exit(1);
});

// Graceful shutdown on SIGINT (Ctrl+C)
process.on("SIGINT", () => {
  logger.info("Received SIGINT, shutting down gracefully");
  process.exit(0);
});

// Graceful shutdown on SIGTERM
process.on("SIGTERM", () => {
  logger.info("Received SIGTERM, shutting down gracefully");
  process.exit(0);
});

// Run the main function
await main();
