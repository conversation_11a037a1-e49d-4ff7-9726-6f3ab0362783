# Scripts

This directory contains utility scripts for the Finanze.Pro backend.

## Currency Rate Updates

### `update-currency-rates.ts`

Updates exchange rates for all supported currencies by fetching the latest rates from CurrencyAPI.com and storing them in the database.

**Usage:**
```bash
# Run the script directly
bun run scripts/update-currency-rates.ts

# Or use the npm script
bun run update-rates
```

**Environment Variables Required:**
- `DATABASE_URL`: PostgreSQL connection string
- `CURRENCYAPICOM_KEY`: API key for CurrencyAPI.com
- `LOG_LEVEL`: Logging level (debug, info, warn, error) - optional, defaults to 'info'

**What it does:**
1. Fetches latest exchange rates for all supported currency pairs
2. Stores rates in the database using upsert operations (updates existing rates)
3. Provides detailed logging of the process
4. <PERSON><PERSON> errors gracefully and continues processing other currencies if one fails
5. Adds small delays between API calls to respect rate limits

**Supported Currencies:**
- EUR (Euro)
- PLN (Polish Złoty)
- UAH (Ukrainian Hryvnia)
- USD (US Dollar)

**Example Output:**
```
[INFO] 🚀 Starting currency rates update process
[INFO] Starting to update all currency rates {"currencies":["EUR","PLN","UAH","USD"],"date":"2024-01-15"}
[INFO] Fetching rates for base currency {"baseCurrency":"EUR"}
[INFO] Received rates from API {"baseCurrency":"EUR","targetCurrencies":["PLN","UAH","USD"],"responseDataKeys":["PLN","UAH","USD"],"lastUpdated":"2024-01-15T10:30:00Z"}
[INFO] Storing rates in database {"count":12}
[INFO] Successfully stored rates in database {"count":12}
[INFO] Successfully updated all currency rates {"totalRates":12,"uniquePairs":12,"date":"2024-01-15"}
[INFO] ✅ Currency rates update completed successfully {"durationMs":2341,"durationSeconds":2}
```

**Scheduling:**
This script can be scheduled to run periodically using cron jobs or similar scheduling systems:

```bash
# Run every hour at minute 0
0 * * * * cd /path/to/project && bun run update-rates

# Run every day at 9 AM
0 9 * * * cd /path/to/project && bun run update-rates
```
