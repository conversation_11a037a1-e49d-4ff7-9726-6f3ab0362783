import logger from "~/lib/logger";
import createWorker from "~/tasks/worker-factory";
import workers from "~/tasks/worker-registry";

const main = () => {
  for (const worker of workers) {
    try {
      createWorker(worker.queueName, worker.processor);
      logger.info({ queueName: worker.queueName }, "✅ Worker created and started");
    } catch (error) {
      logger.error(
        { queueName: worker.queueName, error: (error as Error).message, stack: (error as Error).stack },
        "❌ Failed to create worker"
      );
    }
  }
};

main();
