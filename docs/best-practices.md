# Best Practices Guidelines

## Code Style & Formatting

- Follow TypeScript strict mode guidelines
- Use Prettier for code formatting with 120 character line limit
- Follow ESLint rules configured in the project
- Use import sorting as configured in `.prettierrc`
- Run `bun run format` before committing changes

## Architecture

- Use HonoJS routing patterns with OpenAPI/Zod for validation
- Follow the project structure with clear separation of concerns
- Use the router factory pattern from `app/lib/router.ts`
- Leverage middleware for cross-cutting concerns

## Error Handling

- Use the centralized error handler
- Return structured error responses with appropriate status codes
- Validate all inputs with Zod schemas
- Add OpenAPI documentation for all endpoints

## Database

- Use Drizzle ORM for database access
- Follow the schema patterns in `app/db/schemas`
- Use migrations for schema changes
- Leverage the common schema helpers for IDs and timestamps

## Environment & Configuration

- Use the typed environment configuration in `app/lib/env.ts`
- Add new environment variables to the schema with proper validation
- Never hardcode configuration values

## Logging

- Use the logger middleware for HTTP requests
- Use the logger instance for application logging
- Include contextual information in log messages
- Follow log level guidelines (debug for development, info for normal operations)

## Testing

- Write tests for all business logic
- Use Vitest for testing
- Mock external dependencies
- Test error cases and edge conditions