import type { AppBindings } from "~/types";

import { OpenAPIHono } from "@hono/zod-openapi";

export default function createRouter() {
  return new OpenAPIHono<AppBindings>({
    strict: false,
    defaultHook: (result, c) => {
      if (!result.success) {
        return c.json(
          {
            success: false,
            errors: result.error.issues.map((issue) => issue.message),
            message: result.error.message || "Bad request",
          },
          422
        );
      }
    },
  });
}
