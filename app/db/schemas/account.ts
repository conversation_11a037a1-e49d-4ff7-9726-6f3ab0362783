import { boolean, decimal, pgEnum, pgTable, text, uuid } from "drizzle-orm/pg-core";
import { createSelectSchema } from "drizzle-zod";

import { accountTypes } from "~/common/schemas";

import { accountGroups } from "./account_group";
import { id, timestamps } from "./common";
import { currency } from "./currency";
import { users } from "./user";

export const accountType = pgEnum("account_type", accountTypes);

export const accounts = pgTable("accounts", {
  id: id(),
  userId: uuid()
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  groupId: uuid().references(() => accountGroups.id, { onDelete: "set null" }),
  name: text().notNull(),
  currency: currency(),
  type: accountType().notNull(),
  description: text(),
  color: text(),
  overdraftLimit: decimal({ precision: 15, scale: 4 }),
  isActive: boolean().notNull().default(true),
  openingBalance: decimal({ precision: 15, scale: 4 }).notNull().default("0.0000"),
  currentBalance: decimal({ precision: 15, scale: 4 }).notNull().default("0.0000"),
  baseOpeningBalance: decimal({ precision: 15, scale: 4 }).notNull().default("0.0000"),
  baseCurrentBalance: decimal({ precision: 15, scale: 4 }).notNull().default("0.0000"),
  ...timestamps,
});

export const accountSelectSchema = createSelectSchema(accounts);
