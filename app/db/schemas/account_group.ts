import { index, pgTable, text, uuid } from "drizzle-orm/pg-core";
import { createSelectSchema } from "drizzle-zod";

import { id, timestamps } from "./common";
import { users } from "./user";

export const accountGroups = pgTable(
  "account_groups",
  {
    id: id(),
    userId: uuid()
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    name: text().notNull(),
    color: text(),
    iconUrl: text(),
    ...timestamps,
  },
  (table) => [index("idx_account_groups_user_id").on(table.userId)]
);

export const accountGroupSelectSchema = createSelectSchema(accountGroups);
