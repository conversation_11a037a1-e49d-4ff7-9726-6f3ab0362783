import { boolean, pgTable, text, uniqueIndex } from "drizzle-orm/pg-core";
import { createSelectSchema } from "drizzle-zod";

import { id, lower, timestamps } from "./common";
import { currency } from "./currency";

export const users = pgTable(
  "users",
  {
    id: id(),
    email: text().notNull(),
    passwordHash: text().notNull(),
    name: text(),
    baseCurrency: currency(),
    isActive: boolean().notNull().default(true),
    ...timestamps,
  },
  (table) => [uniqueIndex("unq_users_email").on(lower(table.email))]
);

export const userSelectSchema = createSelectSchema(users);
