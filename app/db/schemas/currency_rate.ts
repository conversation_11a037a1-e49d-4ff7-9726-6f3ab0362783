import { date, decimal, pgTable, primaryKey } from "drizzle-orm/pg-core";

import { timestamps } from "./common";
import { currency } from "./currency";

export const currencyRates = pgTable(
  "currency_rates",
  {
    currencyFrom: currency(),
    currencyTo: currency(),
    rateDate: date().notNull(),
    rate: decimal({ precision: 15, scale: 4 }).notNull().default("1.0000"),
    ...timestamps,
  },
  (table) => [
    primaryKey({ name: "pk_currency_rates", columns: [table.currencyFrom, table.currencyTo, table.rateDate] }),
  ]
);
