CREATE TYPE "public"."account_type" AS ENUM('cash', 'card', 'bank_account', 'savings', 'loan', 'other');
--> statement-breakpoint
CREATE TABLE "accounts" (
	"id" uuid PRIMARY KEY NOT NULL,
	"user_id" uuid NOT NULL,
	"group_id" uuid,
	"name" text NOT NULL,
	"currency" "currency" DEFAULT 'USD' NOT NULL,
	"type" "account_type" NOT NULL,
	"description" text,
	"color" text,
	"overdraft_limit" numeric(15, 4),
	"is_active" boolean DEFAULT true NOT NULL,
	"opening_balance" numeric(15, 4) DEFAULT '0.0000' NOT NULL,
	"current_balance" numeric(15, 4) DEFAULT '0.0000' NOT NULL,
	"base_opening_balance" numeric(15, 4) DEFAULT '0.0000' NOT NULL,
	"base_current_balance" numeric(15, 4) DEFAULT '0.0000' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "account_groups" (
	"id" uuid PRIMARY KEY NOT NULL,
	"user_id" uuid NOT NULL,
	"name" text NOT NULL,
	"color" text,
	"icon_url" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "accounts" ADD CONSTRAINT "accounts_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;
--> statement-breakpoint
ALTER TABLE "accounts" ADD CONSTRAINT "accounts_group_id_account_groups_id_fk" FOREIGN KEY ("group_id") REFERENCES "public"."account_groups"("id") ON DELETE set null ON UPDATE no action;
--> statement-breakpoint
ALTER TABLE "account_groups" ADD CONSTRAINT "account_groups_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;
--> statement-breakpoint
CREATE INDEX "idx_accounts_user_id" ON "accounts" USING btree ("user_id");
--> statement-breakpoint
CREATE INDEX "idx_accounts_group_id" ON "accounts" USING btree ("group_id");
--> statement-breakpoint
CREATE INDEX "idx_account_groups_user_id" ON "account_groups" USING btree ("user_id");
