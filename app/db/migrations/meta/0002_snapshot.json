{"id": "46b6625b-4716-4736-b61f-16c65071f779", "prevId": "89c6af97-9546-4188-93bd-7682724dd033", "version": "7", "dialect": "postgresql", "tables": {"public.accounts": {"name": "accounts", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "group_id": {"name": "group_id", "type": "uuid", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "currency", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'USD'"}, "type": {"name": "type", "type": "account_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "color": {"name": "color", "type": "text", "primaryKey": false, "notNull": false}, "overdraft_limit": {"name": "overdraft_limit", "type": "numeric(15, 4)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "opening_balance": {"name": "opening_balance", "type": "numeric(15, 4)", "primaryKey": false, "notNull": true, "default": "'0.0000'"}, "current_balance": {"name": "current_balance", "type": "numeric(15, 4)", "primaryKey": false, "notNull": true, "default": "'0.0000'"}, "base_opening_balance": {"name": "base_opening_balance", "type": "numeric(15, 4)", "primaryKey": false, "notNull": true, "default": "'0.0000'"}, "base_current_balance": {"name": "base_current_balance", "type": "numeric(15, 4)", "primaryKey": false, "notNull": true, "default": "'0.0000'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_accounts_user_id": {"name": "idx_accounts_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_accounts_group_id": {"name": "idx_accounts_group_id", "columns": [{"expression": "group_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"accounts_user_id_users_id_fk": {"name": "accounts_user_id_users_id_fk", "tableFrom": "accounts", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "accounts_group_id_account_groups_id_fk": {"name": "accounts_group_id_account_groups_id_fk", "tableFrom": "accounts", "tableTo": "account_groups", "columnsFrom": ["group_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.account_groups": {"name": "account_groups", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "color": {"name": "color", "type": "text", "primaryKey": false, "notNull": false}, "icon_url": {"name": "icon_url", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_account_groups_user_id": {"name": "idx_account_groups_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"account_groups_user_id_users_id_fk": {"name": "account_groups_user_id_users_id_fk", "tableFrom": "account_groups", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.currency_rates": {"name": "currency_rates", "schema": "", "columns": {"currency_from": {"name": "currency_from", "type": "currency", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'USD'"}, "currency_to": {"name": "currency_to", "type": "currency", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'USD'"}, "rate_date": {"name": "rate_date", "type": "date", "primaryKey": false, "notNull": true}, "rate": {"name": "rate", "type": "numeric(15, 4)", "primaryKey": false, "notNull": true, "default": "'1.0000'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"pk_currency_rates": {"name": "pk_currency_rates", "columns": ["currency_from", "currency_to", "rate_date"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "password_hash": {"name": "password_hash", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "base_currency": {"name": "base_currency", "type": "currency", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'USD'"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"unq_users_email": {"name": "unq_users_email", "columns": [{"expression": "lower(\"email\")", "asc": true, "isExpression": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.account_type": {"name": "account_type", "schema": "public", "values": ["cash", "card", "bank_account", "savings", "loan", "other"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}