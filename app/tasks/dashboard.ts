import type { OpenAPIHono } from "@hono/zod-openapi";
import type { AppBindings } from "~/types";

import { createBullBoard } from "@bull-board/api";
import { BullMQAdapter } from "@bull-board/api/bullMQAdapter";
import { HonoAdapter } from "@bull-board/hono";
import { serveStatic } from "hono/bun";

import { commonQueue, mailsQueue } from "./queues";

export const basePath = "/bull-dashboard";

const registerBullDashboard = (app: OpenAPIHono<AppBindings>) => {
  const serverAdapter = new HonoAdapter(serveStatic);

  createBullBoard({
    queues: [
      new BullMQAdapter(commonQueue, { description: "Common queue", displayName: "Common" }),
      new BullMQAdapter(mailsQueue, { description: "Mails queue", displayName: "Mails" }),
    ],
    serverAdapter,
  });

  serverAdapter.setBasePath(basePath);
  app.route(basePath, serverAdapter.registerPlugin());
};

export default registerBullDashboard;
