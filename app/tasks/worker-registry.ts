import type { Processor } from "bullmq";

import { QUEUE_COMMON_NAME, QUEUE_MAILS_NAME } from "./constants";
import { commonQueueProcessor, mailsQueueProcessor } from "./processors";

const workers: Array<{ queueName: string; processor: Processor }> = [
  { queueName: QUEUE_COMMON_NAME, processor: commonQueueProcessor },
  { queueName: QUEUE_MAILS_NAME, processor: mailsQueueProcessor },
];

export default workers;
