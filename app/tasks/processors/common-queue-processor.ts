import type { Job, Processor } from "bullmq";

import { TASK_UPDATE_CURRENCY_RATES, updateCurrencyRates } from "~/features/currencies/tasks";
import logger from "~/lib/logger";

const commonQueueProcessor: Processor = async (job: Job) => {
  logger.debug({ jobId: job.id, jobName: job.name }, "Processing common queue job");

  switch (job.name) {
    case TASK_UPDATE_CURRENCY_RATES:
      await updateCurrencyRates(job);
      break;
    default:
      logger.warn({ jobId: job.id, jobName: job.name }, "Unknown job name");
      break;
  }
};

export default commonQueueProcessor;
