// Register all scheduled tasks
import { TASK_UPDATE_CURRENCY_RATES } from "~/features/currencies/tasks";
import logger from "~/lib/logger";

import { commonQueue } from "./queues";

const registerScheduledTasks = async () => {
  // Update currency rates every day at 04:00 UTC
  await commonQueue.upsertJobScheduler(
    TASK_UPDATE_CURRENCY_RATES,
    { pattern: "0 4 * * *" },
    { name: TASK_UPDATE_CURRENCY_RATES }
  );
};

registerScheduledTasks()
  .then(() => {
    logger.info("Scheduled tasks registered");
  })
  .catch((error) => {
    logger.error(
      { error: (error as Error).message, stack: (error as Error).stack },
      "Failed to register scheduled tasks"
    );
  })
  .finally(() => {
    process.exit(0);
  });
