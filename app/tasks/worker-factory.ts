import type { Job } from "bullmq";

import { Worker } from "bullmq";
import IORedis from "ioredis";

import env from "~/lib/env";
import logger from "~/lib/logger";

const createWorker = (queueName: string, processor: (job: Job) => Promise<void>) => {
  const connection = new IORedis(env.QUEUE_REDIS_URL, { maxRetriesPerRequest: null });
  const worker = new Worker(queueName, processor, { connection, concurrency: 1 });

  worker.on("completed", (job) => {
    logger.info({ jobId: job.id, queueName }, `Job ${job.id} has been completed`);
  });

  worker.on("failed", (job, error) => {
    logger.error({ jobId: job?.id ?? "unknown", error: error.message, stack: error.stack, queueName }, "Job failed");
  });

  worker.on("error", (error) => {
    logger.error({ error: error.message, stack: error.stack, queueName }, "Worker error");
  });

  return worker;
};

export default createWorker;
