import { z } from "@hono/zod-openapi";

import { currencies, defaultCurrency } from "~/lib/currencies";

export const currency = (_?: string) =>
  z.enum(currencies).openapi("Currency", { description: "ISO 4217 currency code", example: defaultCurrency });

export const decimal = () =>
  z
    .string()
    .regex(/^(-|\+)?((\d+(\.\d*)?)|(\.\d+))$/)
    .openapi("Decimal", {
      description: "Decimal number with up to 4 decimal places",
      example: "100.2500",
    });

export const accountTypes = ["cash", "card", "bank_account", "savings", "loan", "other"] as const;

export const accountType = (description?: string) =>
  z.enum(accountTypes).openapi("AccountType", { description, example: "cash" });
