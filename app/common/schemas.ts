import { z } from "@hono/zod-openapi";

import { currencies, defaultCurrency } from "~/lib/currencies";

export const currency = (description?: string) =>
  z.enum(currencies).openapi("Currency", { description, example: defaultCurrency });

export const accountTypes = ["cash", "card", "bank_account", "savings", "loan", "other"] as const;

export const accountType = (description?: string) =>
  z.enum(accountTypes).openapi("AccountType", { description, example: "cash" });
