/**
 * Creates a new object excluding the specified keys
 * @param obj The source object
 * @param keys Keys to exclude from the result
 * @returns A new object without the excluded keys
 */
export function omit<T extends Record<string, unknown>, K extends keyof T>(obj: T, keys: K[]): Omit<T, K> {
  const result = { ...obj };

  for (const key of keys) {
    delete result[key];
  }

  return result as Omit<T, K>;
}
