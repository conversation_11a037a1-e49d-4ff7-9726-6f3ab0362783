import { pinoLogger } from "hono-pino";

import { omit } from "~/common/utils";
import logger from "~/lib/logger";

export default function loggerMiddleware() {
  return pinoLogger({
    pino: logger,
    http: {
      onReqBindings: (c) => ({
        req: {
          url: c.req.path,
          method: c.req.method,
          headers: omit(c.req.header(), ["authorization", "cookie"]),
        },
      }),
    },
  });
}
