import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "hono";
import type { ContentfulStatusCode } from "hono/utils/http-status";

import env from "~/lib/env";

const errorHandler: ErrorHandler = (err, c) => {
  const statusCode = "status" in err ? (err.status as ContentfulStatusCode) : 500;
  const message = err.message || "Internal Server Error";

  return c.json({ success: false, message, stack: env.NODE_ENV === "development" ? err.stack : undefined }, statusCode);
};

export default errorHandler;
