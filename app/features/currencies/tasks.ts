import type { Job } from "bullmq";

import logger from "~/lib/logger";

import { updateAllRates } from "./actions";

export const TASK_UPDATE_CURRENCY_RATES = "update-currency-rates";

export const updateCurrencyRates = async (job: Job) => {
  logger.debug({ jobId: job.id }, "Starting currency rates update task");

  await updateAllRates(job);

  logger.debug({ jobId: job.id }, "Currency rates update task completed");
};
