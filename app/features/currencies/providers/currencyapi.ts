// Currency rates provider using CurrencyApi.com service
// https://www.currencyapi.com/

// Common types
interface CurrencyRate {
  code: string;
  value: number;
}

interface Meta {
  last_updated_at: string;
}

// Status endpoint types
interface StatusQuota {
  total: number;
  used: number;
  remaining: number;
}

interface StatusResponse {
  account_id: number;
  quotas: {
    month: StatusQuota;
    grace: StatusQuota;
  };
}

// Currencies endpoint types
interface CurrencyInfo {
  symbol: string;
  name: string;
  symbol_native: string;
  decimal_digits: number;
  rounding: number;
  code: string;
  name_plural: string;
  type: "fiat" | "metal" | "crypto";
  countries: string[];
}

interface CurrenciesResponse {
  data: Record<string, CurrencyInfo>;
}

// Base parameter type for API calls
type BaseParams = Record<string, string | undefined>;

interface CurrenciesParams extends BaseParams {
  currencies?: string;
  type?: "fiat" | "metal" | "crypto";
}

// Latest/Historical/Convert endpoint types
interface ExchangeRatesResponse {
  meta: Meta;
  data: Record<string, CurrencyRate>;
}

interface ExchangeRatesParams extends BaseParams {
  base_currency?: string;
  currencies?: string;
  type?: "fiat" | "metal" | "crypto";
}

interface HistoricalParams extends ExchangeRatesParams {
  date: string; // Required for historical endpoint
}

interface ConvertParams extends ExchangeRatesParams {
  value: string; // Required for convert endpoint
  date?: string;
}

// Range endpoint types
interface RangeDataPoint {
  datetime: string;
  currencies: Record<string, CurrencyRate>;
}

interface RangeResponse {
  data: RangeDataPoint[];
}

interface RangeParams extends BaseParams {
  datetime_start: string; // Required
  datetime_end: string; // Required
  accuracy?: "day" | "hour" | "quarter_hour" | "minute";
  base_currency?: string;
  currencies?: string;
  type?: "fiat" | "metal" | "crypto";
}

class CurrencyAPI {
  private readonly baseUrl = "https://api.currencyapi.com/v3/";
  private readonly headers: Record<string, string>;

  constructor(apiKey = "") {
    this.headers = {
      apikey: apiKey,
    };
  }

  private async call<T>(endpoint: string, params: BaseParams = {}): Promise<T> {
    // Filter out undefined values and convert all values to strings
    const filteredParams: Record<string, string> = {};
    for (const [key, value] of Object.entries(params)) {
      if (value !== undefined) {
        filteredParams[key] = String(value);
      }
    }

    const paramString = new URLSearchParams(filteredParams).toString();

    return fetch(`${this.baseUrl}${endpoint}?${paramString}`, { headers: this.headers })
      .then((response) => response.json())
      .then((data: unknown) => data as T);
  }

  status(): Promise<StatusResponse> {
    return this.call<StatusResponse>("status");
  }

  currencies(params?: CurrenciesParams): Promise<CurrenciesResponse> {
    return this.call<CurrenciesResponse>("currencies", params);
  }

  latest(params?: ExchangeRatesParams): Promise<ExchangeRatesResponse> {
    return this.call<ExchangeRatesResponse>("latest", params);
  }

  historical(params: HistoricalParams): Promise<ExchangeRatesResponse> {
    return this.call<ExchangeRatesResponse>("historical", params);
  }

  range(params: RangeParams): Promise<RangeResponse> {
    return this.call<RangeResponse>("range", params);
  }

  convert(params: ConvertParams): Promise<ExchangeRatesResponse> {
    return this.call<ExchangeRatesResponse>("convert", params);
  }
}

export default CurrencyAPI;

// Export types for use in other modules
export type {
  CurrencyRate,
  Meta,
  StatusResponse,
  CurrencyInfo,
  CurrenciesResponse,
  CurrenciesParams,
  ExchangeRatesResponse,
  ExchangeRatesParams,
  HistoricalParams,
  ConvertParams,
  RangeResponse,
  RangeParams,
};
