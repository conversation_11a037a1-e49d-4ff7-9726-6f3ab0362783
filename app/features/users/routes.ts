import { createRoute } from "@hono/zod-openapi";

import { errorSchema, jsonContent, requestBody, validationErrorSchema } from "~/helpers/openapi";
import { StatusCodes } from "~/lib/status-codes";

import { userCreateSchema, userResponseSchema } from "./schemas";

const tags = ["Users"];

export const create = createRoute({
  tags,

  path: "/users",
  method: "post",
  operationId: "createUser",
  summary: "Create user",
  description:
    "Create (register) a new user. It returns the created user, but access tokens will not be generated " +
    "and should be requested separately.",
  request: {
    body: requestBody(userCreateSchema, "User to create"),
  },
  responses: {
    [StatusCodes.CREATED]: json<PERSON>ontent(userResponseSchema, "User created"),
    [StatusCodes.BAD_REQUEST]: errorSchema("Bad request"),
    [StatusCodes.CONFLICT]: errorSchema("User already exists"),
    [StatusCodes.UNPROCESSABLE_CONTENT]: validationErrorSchema("Validation error"),
  },
});

export const me = createRoute({
  tags,

  path: "/users/me",
  method: "get",
  operationId: "getCurrentUser",
  summary: "Current user",
  description: "Get current user details",
  security: [{ JWT: [] }],
  responses: {
    [StatusCodes.OK]: jsonContent(userResponseSchema, "Current user"),
    [StatusCodes.BAD_REQUEST]: errorSchema("Bad request"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
  },
});

export type CreateRoute = typeof create;
export type MeRoute = typeof me;
