import { z } from "@hono/zod-openapi";

import { currency } from "~/common/schemas";

export const userCreateSchema = z
  .object({
    email: z
      .string({ message: "Email is required" })
      .email({ message: "Invalid email address" })
      .openapi({ example: "<EMAIL>" }),
    password: z.string().min(8).openapi({ example: "SuperSecret123" }),
    name: z.string().nullable().openapi({ example: "John Doe" }),
    baseCurrency: currency("Base currency for the user"),
  })
  .openapi("UserCreateRequest");

export const userResponseSchema = z
  .object({
    id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    email: z.string().email().openapi({ example: "<EMAIL>" }),
    name: z.string().nullable().openapi({ example: "John Doe" }),
    baseCurrency: currency("Base currency for the user"),
    isActive: z.boolean().optional().openapi({ example: true }),
  })
  .openapi("User");
