import type { App<PERSON>out<PERSON><PERSON><PERSON><PERSON> } from "~/types";
import type { <PERSON><PERSON><PERSON>out<PERSON>, <PERSON><PERSON>out<PERSON> } from "./routes";

import { StatusCodes } from "~/lib/status-codes";

import { createUser } from "./actions";
import { mapUserResponse } from "./mappers";

export const create: AppRouteHandler<CreateRoute> = async (c) => {
  const data = c.req.valid("json");

  const user = await createUser(data);

  return c.json(mapUserResponse(user), StatusCodes.CREATED);
};

export const me: AppRouteHandler<MeRoute> = (c) => {
  const user = c.get("user");
  return c.json(mapUserResponse(user), StatusCodes.OK);
};
