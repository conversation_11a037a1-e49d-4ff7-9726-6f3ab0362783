import type { CreateUserData, User } from "./types";

import { eq } from "drizzle-orm";
import { HTTPException } from "hono/http-exception";

import { createPasswordHash } from "~/common/actions";
import db from "~/db";
import { lower, users } from "~/db/schemas";
import { StatusCodes } from "~/lib/status-codes";

export const createUser = async ({ password, ...data }: CreateUserData): Promise<User> => {
  const existingUser = await getUserByEmail(data.email);
  if (existingUser) {
    throw new HTTPException(StatusCodes.CONFLICT, { message: "User already exists" });
  }

  const passwordHash = await createPasswordHash(password);
  const [user] = await db
    .insert(users)
    .values({
      ...data,
      passwordHash,
    })
    .returning();

  if (!user) {
    throw new HTTPException(StatusCodes.BAD_REQUEST, { message: "Failed to create user" });
  }

  return user;
};

export const getUserByEmail = async (email: string): Promise<User | undefined> => {
  const [user] = await db
    .select()
    .from(users)
    .where(eq(lower(users.email), email.toLowerCase()));

  return user;
};

export const getUserById = async (id: string): Promise<User | undefined> => {
  const [user] = await db.select().from(users).where(eq(users.id, id));

  return user;
};
