import type { AppBindings } from "~/types";

import { createMiddleware } from "hono/factory";
import { HTTPException } from "hono/http-exception";

import { StatusCodes } from "~/lib/status-codes";

import { getUserById } from "./actions";

export const currentUser = () =>
  createMiddleware<AppBindings>(async (c, next) => {
    const logger = c.get("logger");

    const jwtPayload = c.get("jwtPayload");
    if (!jwtPayload.sub) {
      logger.warn({ jwtPayload }, "Failed to get user from JWT payload");
      throw new HTTPException(StatusCodes.UNAUTHORIZED, { message: "Unauthorized" });
    }

    const user = await getUserById(jwtPayload.sub);
    if (!user) {
      logger.warn({ user: jwtPayload.sub }, "Failed to get user from database");
      throw new HTTPException(StatusCodes.UNAUTHORIZED, { message: "Unauthorized" });
    }

    if (!user.isActive) {
      logger.warn({ user: jwtPayload.sub }, "User is not active");
      throw new HTTPException(StatusCodes.UNAUTHORIZED, { message: "Unauthorized" });
    }

    c.set("user", user);

    await next();
  });
