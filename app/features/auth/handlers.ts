import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "~/types";
import type { TokenRoute } from "./routes";

import { setCookie } from "hono/cookie";
import { HTTPException } from "hono/http-exception";

import { verifyPassword } from "~/common/actions";
import { getUserByEmail } from "~/features/users/actions";
import { StatusCodes } from "~/lib/status-codes";

import { createAccessToken } from "./actions";

export const token: App<PERSON><PERSON>eH<PERSON><PERSON><TokenRoute> = async (c) => {
  const data = c.req.valid("json");

  const user = await getUserByEmail(data.email);
  if (!user) {
    throw new HTTPException(StatusCodes.BAD_REQUEST, { message: "Wrong email or password" });
  }

  const isValid = await verifyPassword(data.password, user.passwordHash);
  if (!isValid) {
    throw new HTTPException(StatusCodes.BAD_REQUEST, { message: "Wrong email or password" });
  }

  const accessToken = await createAccessToken(user.id);

  setCookie(c, "access_token", accessToken, {
    httpOnly: true,
    secure: true,
    sameSite: "lax",
    path: "/",
    maxAge: 7 * 24 * 60 * 60, // 7 days
    expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
  });

  return c.json({ accessToken }, StatusCodes.OK);
};
