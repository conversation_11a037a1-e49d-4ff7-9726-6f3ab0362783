import { createRoute } from "@hono/zod-openapi";

import { errorSchema, jsonContent, requestBody, validationErrorSchema } from "~/helpers/openapi";
import { StatusCodes } from "~/lib/status-codes";

import { tokenRequestSchema, tokenResponseSchema } from "./schemas";

const tags = ["Auth"];

export const token = createRoute({
  tags,

  path: "/auth/token",
  method: "post",
  operationId: "getToken",
  summary: "Get access token",
  description:
    "Get access token for the user. Generated token should be used in the `Authorization` header or `access_token` cookie.",
  request: {
    body: requestBody(tokenRequestSchema, "Token request"),
  },
  responses: {
    [StatusCodes.OK]: json<PERSON>ontent(tokenResponseSchema, "Access token"),
    [StatusCodes.BAD_REQUEST]: errorSchema("Bad request"),
    [StatusCodes.UNPROCESSABLE_CONTENT]: validationErrorSchema("Validation error"),
  },
});

export type TokenRoute = typeof token;
