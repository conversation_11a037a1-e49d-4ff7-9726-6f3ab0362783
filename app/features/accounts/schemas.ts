import { z } from "@hono/zod-openapi";

import { accountType, currency, decimal } from "~/common/schemas";

// Account Group Schemas
export const accountGroupCreateSchema = z
  .object({
    name: z.string().min(1).max(100).openapi({ example: "Personal Accounts" }),
    color: z.string().optional().openapi({ example: "#3B82F6" }),
    iconUrl: z.string().url().optional().openapi({ example: "https://example.com/icon.png" }),
  })
  .openapi("AccountGroupCreateRequest");

export const accountGroupUpdateSchema = z
  .object({
    name: z.string().min(1).max(100).optional().openapi({ example: "Personal Accounts" }),
    color: z.string().optional().openapi({ example: "#3B82F6" }),
    iconUrl: z.string().url().optional().openapi({ example: "https://example.com/icon.png" }),
  })
  .openapi("AccountGroupUpdateRequest");

export const accountGroupResponseSchema = z
  .object({
    id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    name: z.string().openapi({ example: "Personal Accounts" }),
    color: z.string().nullable().openapi({ example: "#3B82F6" }),
    iconUrl: z.string().nullable().openapi({ example: "https://example.com/icon.png" }),
    createdAt: z.string().datetime().openapi({ example: "2024-01-01T00:00:00.000Z" }),
    updatedAt: z.string().datetime().openapi({ example: "2024-01-01T00:00:00.000Z" }),
  })
  .openapi("AccountGroup");

// Account Schemas
export const accountCreateSchema = z
  .object({
    groupId: z.string().uuid().nullable().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    name: z.string().min(3).max(100).openapi({ example: "Main Checking Account" }),
    currency: currency("Account currency"),
    type: accountType("Account type"),
    description: z.string().nullish().openapi({ example: "Primary checking account for daily expenses" }),
    color: z.string().nullish().openapi({ example: "#10B981" }),
    overdraftLimit: decimal().nullish(),
    openingBalance: decimal(),
  })
  .openapi("AccountCreateRequest");

export const accountUpdateSchema = z
  .object({
    groupId: z.string().uuid().nullish().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    name: z.string().min(3).max(100).optional().openapi({ example: "Main Checking Account" }),
    currency: currency("Account currency").optional(),
    type: accountType("Account type").optional(),
    description: z.string().nullish().openapi({ example: "Primary checking account for daily expenses" }),
    color: z.string().nullish().openapi({ example: "#10B981" }),
    overdraftLimit: decimal().nullish(),
    isActive: z.boolean().optional().openapi({ example: true }),
  })
  .openapi("AccountUpdateRequest");

export const accountBalanceUpdateSchema = z
  .object({
    currentBalance: decimal(),
  })
  .openapi("AccountBalanceUpdateRequest");

export const accountResponseSchema = z
  .object({
    id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    groupId: z.string().uuid().nullable().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    group: accountGroupResponseSchema.optional(),
    name: z.string().openapi({ example: "Main Checking Account" }),
    currency: currency("Account currency"),
    type: accountType("Account type"),
    description: z.string().nullable().openapi({ example: "Primary checking account for daily expenses" }),
    color: z.string().nullable().openapi({ example: "#10B981" }),
    overdraftLimit: decimal(),
    isActive: z.boolean().openapi({ example: true }),
    openingBalance: decimal(),
    currentBalance: decimal(),
    baseOpeningBalance: decimal(),
    baseCurrentBalance: decimal(),
    createdAt: z.string().datetime().openapi({ example: "2024-01-01T00:00:00.000Z" }),
    updatedAt: z.string().datetime().openapi({ example: "2024-01-01T00:00:00.000Z" }),
  })
  .openapi("Account");
