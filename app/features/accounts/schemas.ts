import { z } from "@hono/zod-openapi";

import { accountType, currency } from "~/common/schemas";

// Account Group Schemas
export const accountGroupCreateSchema = z
  .object({
    name: z.string().min(1).max(100).openapi({ example: "Personal Accounts" }),
    color: z.string().optional().openapi({ example: "#3B82F6" }),
    iconUrl: z.string().url().optional().openapi({ example: "https://example.com/icon.png" }),
  })
  .openapi("AccountGroupCreateRequest");

export const accountGroupUpdateSchema = z
  .object({
    name: z.string().min(1).max(100).optional().openapi({ example: "Personal Accounts" }),
    color: z.string().optional().openapi({ example: "#3B82F6" }),
    iconUrl: z.string().url().optional().openapi({ example: "https://example.com/icon.png" }),
  })
  .openapi("AccountGroupUpdateRequest");

export const accountGroupResponseSchema = z
  .object({
    id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    name: z.string().openapi({ example: "Personal Accounts" }),
    color: z.string().nullable().openapi({ example: "#3B82F6" }),
    iconUrl: z.string().nullable().openapi({ example: "https://example.com/icon.png" }),
    createdAt: z.string().datetime().openapi({ example: "2024-01-01T00:00:00.000Z" }),
    updatedAt: z.string().datetime().openapi({ example: "2024-01-01T00:00:00.000Z" }),
  })
  .openapi("AccountGroup");

// Account Schemas
export const accountCreateSchema = z
  .object({
    groupId: z.string().uuid().optional().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    name: z.string().min(1).max(100).openapi({ example: "Main Checking Account" }),
    currency: currency("Account currency"),
    type: accountType("Account type"),
    description: z.string().optional().openapi({ example: "Primary checking account for daily expenses" }),
    color: z.string().optional().openapi({ example: "#10B981" }),
    overdraftLimit: z.string().optional().openapi({ example: "1000.0000" }),
    openingBalance: z.string().openapi({ example: "1500.0000" }),
  })
  .openapi("AccountCreateRequest");

export const accountUpdateSchema = z
  .object({
    groupId: z.string().uuid().optional().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    name: z.string().min(1).max(100).optional().openapi({ example: "Main Checking Account" }),
    currency: currency("Account currency").optional(),
    type: accountType("Account type").optional(),
    description: z.string().optional().openapi({ example: "Primary checking account for daily expenses" }),
    color: z.string().optional().openapi({ example: "#10B981" }),
    overdraftLimit: z.string().optional().openapi({ example: "1000.0000" }),
    isActive: z.boolean().optional().openapi({ example: true }),
  })
  .openapi("AccountUpdateRequest");

export const accountBalanceUpdateSchema = z
  .object({
    currentBalance: z.string().openapi({ example: "2500.0000" }),
  })
  .openapi("AccountBalanceUpdateRequest");

export const accountResponseSchema = z
  .object({
    id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    groupId: z.string().uuid().nullable().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    name: z.string().openapi({ example: "Main Checking Account" }),
    currency: currency("Account currency"),
    type: accountType("Account type"),
    description: z.string().nullable().openapi({ example: "Primary checking account for daily expenses" }),
    color: z.string().nullable().openapi({ example: "#10B981" }),
    overdraftLimit: z.string().nullable().openapi({ example: "1000.0000" }),
    isActive: z.boolean().openapi({ example: true }),
    openingBalance: z.string().openapi({ example: "1500.0000" }),
    currentBalance: z.string().openapi({ example: "2500.0000" }),
    baseOpeningBalance: z.string().openapi({ example: "1500.0000" }),
    baseCurrentBalance: z.string().openapi({ example: "2500.0000" }),
    createdAt: z.string().datetime().openapi({ example: "2024-01-01T00:00:00.000Z" }),
    updatedAt: z.string().datetime().openapi({ example: "2024-01-01T00:00:00.000Z" }),
  })
  .openapi("Account");

export const accountWithGroupResponseSchema = z
  .object({
    id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    groupId: z.string().uuid().nullable().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    group: accountGroupResponseSchema.nullable(),
    name: z.string().openapi({ example: "Main Checking Account" }),
    currency: currency("Account currency"),
    type: accountType("Account type"),
    description: z.string().nullable().openapi({ example: "Primary checking account for daily expenses" }),
    color: z.string().nullable().openapi({ example: "#10B981" }),
    overdraftLimit: z.string().nullable().openapi({ example: "1000.0000" }),
    isActive: z.boolean().openapi({ example: true }),
    openingBalance: z.string().openapi({ example: "1500.0000" }),
    currentBalance: z.string().openapi({ example: "2500.0000" }),
    baseOpeningBalance: z.string().openapi({ example: "1500.0000" }),
    baseCurrentBalance: z.string().openapi({ example: "2500.0000" }),
    createdAt: z.string().datetime().openapi({ example: "2024-01-01T00:00:00.000Z" }),
    updatedAt: z.string().datetime().openapi({ example: "2024-01-01T00:00:00.000Z" }),
  })
  .openapi("AccountWithGroup");
