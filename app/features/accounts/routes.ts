import { createRoute, z } from "@hono/zod-openapi";

import { errorSchema, jsonContent, requestBody, validationErrorSchema } from "~/helpers/openapi";
import { StatusCodes } from "~/lib/status-codes";

import {
  accountBalanceUpdateSchema,
  accountCreateSchema,
  accountGroupCreateSchema,
  accountGroupResponseSchema,
  accountGroupUpdateSchema,
  accountResponseSchema,
  accountUpdateSchema,
} from "./schemas";

const tags = ["Accounts"];

// Account Group Routes
export const listAccountGroups = createRoute({
  tags,
  path: "/account-groups",
  method: "get",
  operationId: "listAccountGroups",
  summary: "List account groups",
  description: "Get all account groups for the current user",
  security: [{ JWT: [] }],
  responses: {
    [StatusCodes.OK]: jsonContent(z.array(accountGroupResponseSchema), "Account groups retrieved"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
  },
});

export const createAccountGroup = createRoute({
  tags,
  path: "/account-groups",
  method: "post",
  operationId: "createAccountGroup",
  summary: "Create account group",
  description: "Create a new account group",
  security: [{ JWT: [] }],
  request: {
    body: requestBody(accountGroupCreateSchema, "Account group to create"),
  },
  responses: {
    [StatusCodes.CREATED]: jsonContent(accountGroupResponseSchema, "Account group created"),
    [StatusCodes.BAD_REQUEST]: errorSchema("Bad request"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
    [StatusCodes.UNPROCESSABLE_CONTENT]: validationErrorSchema("Validation error"),
  },
});

export const getAccountGroup = createRoute({
  tags,
  path: "/account-groups/{id}",
  method: "get",
  operationId: "getAccountGroup",
  summary: "Get account group",
  description: "Get account group by ID",
  security: [{ JWT: [] }],
  request: {
    params: z.object({
      id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    }),
  },
  responses: {
    [StatusCodes.OK]: jsonContent(accountGroupResponseSchema, "Account group retrieved"),
    [StatusCodes.NOT_FOUND]: errorSchema("Account group not found"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
  },
});

export const updateAccountGroup = createRoute({
  tags,
  path: "/account-groups/{id}",
  method: "put",
  operationId: "updateAccountGroup",
  summary: "Update account group",
  description: "Update account group by ID",
  security: [{ JWT: [] }],
  request: {
    params: z.object({
      id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    }),
    body: requestBody(accountGroupUpdateSchema, "Account group data to update"),
  },
  responses: {
    [StatusCodes.OK]: jsonContent(accountGroupResponseSchema, "Account group updated"),
    [StatusCodes.NOT_FOUND]: errorSchema("Account group not found"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
    [StatusCodes.UNPROCESSABLE_CONTENT]: validationErrorSchema("Validation error"),
  },
});

export const deleteAccountGroup = createRoute({
  tags,
  path: "/account-groups/{id}",
  method: "delete",
  operationId: "deleteAccountGroup",
  summary: "Delete account group",
  description: "Delete account group by ID",
  security: [{ JWT: [] }],
  request: {
    params: z.object({
      id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    }),
  },
  responses: {
    [StatusCodes.NO_CONTENT]: {
      description: "Account group deleted",
    },
    [StatusCodes.NOT_FOUND]: errorSchema("Account group not found"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
  },
});

// Account Routes
export const listAccounts = createRoute({
  tags,
  path: "/accounts",
  method: "get",
  operationId: "listAccounts",
  summary: "List accounts",
  description: "Get all accounts for the current user",
  security: [{ JWT: [] }],
  responses: {
    [StatusCodes.OK]: jsonContent(z.array(accountResponseSchema), "Accounts retrieved"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
  },
});

export const createAccount = createRoute({
  tags,
  path: "/accounts",
  method: "post",
  operationId: "createAccount",
  summary: "Create account",
  description: "Create a new account",
  security: [{ JWT: [] }],
  request: {
    body: requestBody(accountCreateSchema, "Account to create"),
  },
  responses: {
    [StatusCodes.CREATED]: jsonContent(accountResponseSchema, "Account created"),
    [StatusCodes.BAD_REQUEST]: errorSchema("Bad request"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
    [StatusCodes.UNPROCESSABLE_CONTENT]: validationErrorSchema("Validation error"),
  },
});

export const getAccount = createRoute({
  tags,
  path: "/accounts/{id}",
  method: "get",
  operationId: "getAccount",
  summary: "Get account",
  description: "Get account by ID",
  security: [{ JWT: [] }],
  request: {
    params: z.object({
      id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    }),
  },
  responses: {
    [StatusCodes.OK]: jsonContent(accountResponseSchema, "Account retrieved"),
    [StatusCodes.NOT_FOUND]: errorSchema("Account not found"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
  },
});

export const updateAccount = createRoute({
  tags,
  path: "/accounts/{id}",
  method: "put",
  operationId: "updateAccount",
  summary: "Update account",
  description: "Update account by ID",
  security: [{ JWT: [] }],
  request: {
    params: z.object({
      id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    }),
    body: requestBody(accountUpdateSchema, "Account data to update"),
  },
  responses: {
    [StatusCodes.OK]: jsonContent(accountResponseSchema, "Account updated"),
    [StatusCodes.NOT_FOUND]: errorSchema("Account not found"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
    [StatusCodes.UNPROCESSABLE_CONTENT]: validationErrorSchema("Validation error"),
  },
});

export const updateAccountBalance = createRoute({
  tags,
  path: "/accounts/{id}/balance",
  method: "patch",
  operationId: "updateAccountBalance",
  summary: "Update account balance",
  description: "Update current balance for an account",
  security: [{ JWT: [] }],
  request: {
    params: z.object({
      id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    }),
    body: requestBody(accountBalanceUpdateSchema, "New balance data"),
  },
  responses: {
    [StatusCodes.OK]: jsonContent(accountResponseSchema, "Account balance updated"),
    [StatusCodes.NOT_FOUND]: errorSchema("Account not found"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
    [StatusCodes.UNPROCESSABLE_CONTENT]: validationErrorSchema("Validation error"),
  },
});

export const deleteAccount = createRoute({
  tags,
  path: "/accounts/{id}",
  method: "delete",
  operationId: "deleteAccount",
  summary: "Delete account",
  description: "Delete account by ID",
  security: [{ JWT: [] }],
  request: {
    params: z.object({
      id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    }),
  },
  responses: {
    [StatusCodes.NO_CONTENT]: {
      description: "Account deleted",
    },
    [StatusCodes.NOT_FOUND]: errorSchema("Account not found"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
  },
});

// Route Types
export type ListAccountGroupsRoute = typeof listAccountGroups;
export type CreateAccountGroupRoute = typeof createAccountGroup;
export type GetAccountGroupRoute = typeof getAccountGroup;
export type UpdateAccountGroupRoute = typeof updateAccountGroup;
export type DeleteAccountGroupRoute = typeof deleteAccountGroup;

export type ListAccountsRoute = typeof listAccounts;
export type CreateAccountRoute = typeof createAccount;
export type GetAccountRoute = typeof getAccount;
export type UpdateAccountRoute = typeof updateAccount;
export type UpdateAccountBalanceRoute = typeof updateAccountBalance;
export type DeleteAccountRoute = typeof deleteAccount;
