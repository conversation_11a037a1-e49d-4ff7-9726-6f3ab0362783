import createRouter from "~/lib/router";

import * as handlers from "./handlers";
import * as routes from "./routes";

const router = createRouter()
  // Account Group routes
  .openapi(routes.listAccountGroups, handlers.listAccountGroups)
  .openapi(routes.createAccountGroup, handlers.createAccountGroup)
  .openapi(routes.getAccountGroup, handlers.getAccountGroup)
  .openapi(routes.updateAccountGroup, handlers.updateAccountGroup)
  .openapi(routes.deleteAccountGroup, handlers.deleteAccountGroup)
  // Account routes
  .openapi(routes.listAccounts, handlers.listAccounts)
  .openapi(routes.createAccount, handlers.createAccount)
  .openapi(routes.getAccount, handlers.getAccount)
  .openapi(routes.updateAccount, handlers.updateAccount)
  .openapi(routes.updateAccountBalance, handlers.updateAccountBalance)
  .openapi(routes.deleteAccount, handlers.deleteAccount);

export default router;
