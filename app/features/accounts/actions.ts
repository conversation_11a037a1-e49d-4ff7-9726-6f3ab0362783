import type {
  Account,
  AccountGroup,
  AccountWithGroup,
  CreateAccountData,
  CreateAccountGroupData,
  UpdateAccountBalanceData,
  UpdateAccountData,
  UpdateAccountGroupData,
} from "./types";

import { and, eq } from "drizzle-orm";
import { HTTPException } from "hono/http-exception";

import db from "~/db";
import { accountGroups, accounts } from "~/db/schemas";
import { convertAmount } from "~/features/currencies/actions";
import { StatusCodes } from "~/lib/status-codes";

// Account Group Actions
export const createAccountGroup = async (
  userId: string,
  data: CreateAccountGroupData
): Promise<AccountGroup> => {
  const [accountGroup] = await db
    .insert(accountGroups)
    .values({
      userId,
      ...data,
    })
    .returning();

  if (!accountGroup) {
    throw new HTTPException(StatusCodes.BAD_REQUEST, { message: "Failed to create account group" });
  }

  return accountGroup;
};

export const getAccountGroupsByUserId = async (userId: string): Promise<AccountGroup[]> => {
  return await db.select().from(accountGroups).where(eq(accountGroups.userId, userId));
};

export const getAccountGroupById = async (id: string, userId: string): Promise<AccountGroup | undefined> => {
  const [accountGroup] = await db
    .select()
    .from(accountGroups)
    .where(and(eq(accountGroups.id, id), eq(accountGroups.userId, userId)));

  return accountGroup;
};

export const updateAccountGroup = async (
  id: string,
  userId: string,
  data: UpdateAccountGroupData
): Promise<AccountGroup> => {
  const [accountGroup] = await db
    .update(accountGroups)
    .set(data)
    .where(and(eq(accountGroups.id, id), eq(accountGroups.userId, userId)))
    .returning();

  if (!accountGroup) {
    throw new HTTPException(StatusCodes.NOT_FOUND, { message: "Account group not found" });
  }

  return accountGroup;
};

export const deleteAccountGroup = async (id: string, userId: string): Promise<void> => {
  const result = await db
    .delete(accountGroups)
    .where(and(eq(accountGroups.id, id), eq(accountGroups.userId, userId)));

  if (result.rowCount === 0) {
    throw new HTTPException(StatusCodes.NOT_FOUND, { message: "Account group not found" });
  }
};

// Account Actions
export const createAccount = async (userId: string, userBaseCurrency: string, data: CreateAccountData): Promise<Account> => {
  // Validate group exists if provided
  if (data.groupId) {
    const group = await getAccountGroupById(data.groupId, userId);
    if (!group) {
      throw new HTTPException(StatusCodes.BAD_REQUEST, { message: "Account group not found" });
    }
  }

  // Calculate base currency amounts
  const baseOpeningBalance = await convertAmount(data.openingBalance, data.currency, userBaseCurrency);
  const baseCurrentBalance = baseOpeningBalance; // Initially same as opening balance

  const [account] = await db
    .insert(accounts)
    .values({
      userId,
      ...data,
      currentBalance: data.openingBalance, // Initially same as opening balance
      baseOpeningBalance,
      baseCurrentBalance,
    })
    .returning();

  if (!account) {
    throw new HTTPException(StatusCodes.BAD_REQUEST, { message: "Failed to create account" });
  }

  return account;
};

export const getAccountsByUserId = async (userId: string): Promise<AccountWithGroup[]> => {
  return await db
    .select({
      id: accounts.id,
      userId: accounts.userId,
      groupId: accounts.groupId,
      name: accounts.name,
      currency: accounts.currency,
      type: accounts.type,
      description: accounts.description,
      color: accounts.color,
      overdraftLimit: accounts.overdraftLimit,
      isActive: accounts.isActive,
      openingBalance: accounts.openingBalance,
      currentBalance: accounts.currentBalance,
      baseOpeningBalance: accounts.baseOpeningBalance,
      baseCurrentBalance: accounts.baseCurrentBalance,
      createdAt: accounts.createdAt,
      updatedAt: accounts.updatedAt,
      group: accountGroups,
    })
    .from(accounts)
    .leftJoin(accountGroups, eq(accounts.groupId, accountGroups.id))
    .where(eq(accounts.userId, userId));
};

export const getAccountById = async (id: string, userId: string): Promise<AccountWithGroup | undefined> => {
  const [account] = await db
    .select({
      id: accounts.id,
      userId: accounts.userId,
      groupId: accounts.groupId,
      name: accounts.name,
      currency: accounts.currency,
      type: accounts.type,
      description: accounts.description,
      color: accounts.color,
      overdraftLimit: accounts.overdraftLimit,
      isActive: accounts.isActive,
      openingBalance: accounts.openingBalance,
      currentBalance: accounts.currentBalance,
      baseOpeningBalance: accounts.baseOpeningBalance,
      baseCurrentBalance: accounts.baseCurrentBalance,
      createdAt: accounts.createdAt,
      updatedAt: accounts.updatedAt,
      group: accountGroups,
    })
    .from(accounts)
    .leftJoin(accountGroups, eq(accounts.groupId, accountGroups.id))
    .where(and(eq(accounts.id, id), eq(accounts.userId, userId)));

  return account;
};

export const updateAccount = async (
  id: string,
  userId: string,
  userBaseCurrency: string,
  data: UpdateAccountData
): Promise<Account> => {
  // Validate group exists if provided
  if (data.groupId) {
    const group = await getAccountGroupById(data.groupId, userId);
    if (!group) {
      throw new HTTPException(StatusCodes.BAD_REQUEST, { message: "Account group not found" });
    }
  }

  // Get current account to check if currency changed
  const currentAccount = await getAccountById(id, userId);
  if (!currentAccount) {
    throw new HTTPException(StatusCodes.NOT_FOUND, { message: "Account not found" });
  }

  let updateData = { ...data };

  // If currency changed, recalculate base amounts
  if (data.currency && data.currency !== currentAccount.currency) {
    const baseOpeningBalance = await convertAmount(currentAccount.openingBalance, data.currency, userBaseCurrency);
    const baseCurrentBalance = await convertAmount(currentAccount.currentBalance, data.currency, userBaseCurrency);
    
    updateData = {
      ...updateData,
      baseOpeningBalance,
      baseCurrentBalance,
    };
  }

  const [account] = await db
    .update(accounts)
    .set(updateData)
    .where(and(eq(accounts.id, id), eq(accounts.userId, userId)))
    .returning();

  if (!account) {
    throw new HTTPException(StatusCodes.NOT_FOUND, { message: "Account not found" });
  }

  return account;
};

export const updateAccountBalance = async (
  id: string,
  userId: string,
  userBaseCurrency: string,
  data: UpdateAccountBalanceData
): Promise<Account> => {
  // Get current account
  const currentAccount = await getAccountById(id, userId);
  if (!currentAccount) {
    throw new HTTPException(StatusCodes.NOT_FOUND, { message: "Account not found" });
  }

  // Calculate base current balance
  const baseCurrentBalance = await convertAmount(data.currentBalance, currentAccount.currency, userBaseCurrency);

  const [account] = await db
    .update(accounts)
    .set({
      currentBalance: data.currentBalance,
      baseCurrentBalance,
    })
    .where(and(eq(accounts.id, id), eq(accounts.userId, userId)))
    .returning();

  if (!account) {
    throw new HTTPException(StatusCodes.NOT_FOUND, { message: "Account not found" });
  }

  return account;
};

export const deleteAccount = async (id: string, userId: string): Promise<void> => {
  const result = await db
    .delete(accounts)
    .where(and(eq(accounts.id, id), eq(accounts.userId, userId)));

  if (result.rowCount === 0) {
    throw new HTTPException(StatusCodes.NOT_FOUND, { message: "Account not found" });
  }
};
