import type { App<PERSON>outeHand<PERSON> } from "~/types";
import type {
  CreateAccountGroupRoute,
  CreateAccountRoute,
  DeleteAccountGroupRoute,
  DeleteAccountRoute,
  GetAccountGroupRoute,
  GetAccountRoute,
  ListAccountGroupsRoute,
  ListAccountsRoute,
  UpdateAccountBalanceRoute,
  UpdateAccountGroupRoute,
  UpdateAccountRoute,
} from "./routes";

import { StatusCodes } from "~/lib/status-codes";

import {
  createAccount as createAccountAction,
  createAccountGroup as createAccountGroupAction,
  deleteAccount as deleteAccountAction,
  deleteAccountGroup as deleteAccountGroupAction,
  getAccountById,
  getAccountGroupById,
  getAccountGroupsByUserId,
  getAccountsByUserId,
  updateAccount as updateAccountAction,
  updateAccountBalance as updateAccountBalanceAction,
  updateAccountGroup as updateAccountGroupAction,
} from "./actions";
import { mapAccountGroupResponse, mapAccountResponse } from "./mappers";

// Account Group Handlers
export const listAccountGroups: AppRouteHandler<ListAccountGroupsRoute> = async (c) => {
  const user = c.get("user");
  const accountGroups = await getAccountGroupsByUserId(user.id);
  return c.json(accountGroups.map(mapAccountGroupResponse), StatusCodes.OK);
};

export const createAccountGroup: AppRouteHandler<CreateAccountGroupRoute> = async (c) => {
  const user = c.get("user");
  const data = c.req.valid("json");

  const accountGroup = await createAccountGroupAction(user.id, data);
  return c.json(mapAccountGroupResponse(accountGroup), StatusCodes.CREATED);
};

export const getAccountGroup: AppRouteHandler<GetAccountGroupRoute> = async (c) => {
  const user = c.get("user");
  const { id } = c.req.valid("param");

  const accountGroup = await getAccountGroupById(id, user.id);
  if (!accountGroup) {
    return c.json({ success: false, message: "Account group not found" }, StatusCodes.NOT_FOUND);
  }

  return c.json(mapAccountGroupResponse(accountGroup), StatusCodes.OK);
};

export const updateAccountGroup: AppRouteHandler<UpdateAccountGroupRoute> = async (c) => {
  const user = c.get("user");
  const { id } = c.req.valid("param");
  const data = c.req.valid("json");

  const accountGroup = await updateAccountGroupAction(id, user.id, data);
  return c.json(mapAccountGroupResponse(accountGroup), StatusCodes.OK);
};

export const deleteAccountGroup: AppRouteHandler<DeleteAccountGroupRoute> = async (c) => {
  const user = c.get("user");
  const { id } = c.req.valid("param");

  await deleteAccountGroupAction(id, user.id);
  return c.body(null, StatusCodes.NO_CONTENT);
};

// Account Handlers
export const listAccounts: AppRouteHandler<ListAccountsRoute> = async (c) => {
  const user = c.get("user");
  const accounts = await getAccountsByUserId(user.id);
  return c.json(accounts.map(mapAccountResponse), StatusCodes.OK);
};

export const createAccount: AppRouteHandler<CreateAccountRoute> = async (c) => {
  const user = c.get("user");
  const data = c.req.valid("json");

  const account = await createAccountAction(user, data);
  return c.json(mapAccountResponse(account), StatusCodes.CREATED);
};

export const getAccount: AppRouteHandler<GetAccountRoute> = async (c) => {
  const user = c.get("user");
  const { id } = c.req.valid("param");

  const account = await getAccountById(id, user.id);
  if (!account) {
    return c.json({ success: false, message: "Account not found" }, StatusCodes.NOT_FOUND);
  }

  return c.json(mapAccountResponse(account), StatusCodes.OK);
};

export const updateAccount: AppRouteHandler<UpdateAccountRoute> = async (c) => {
  const user = c.get("user");
  const { id } = c.req.valid("param");
  const data = c.req.valid("json");

  const account = await updateAccountAction(id, user, data);
  return c.json(mapAccountResponse(account), StatusCodes.OK);
};

export const updateAccountBalance: AppRouteHandler<UpdateAccountBalanceRoute> = async (c) => {
  const user = c.get("user");
  const { id } = c.req.valid("param");
  const data = c.req.valid("json");

  const account = await updateAccountBalanceAction(id, user, data);
  return c.json(mapAccountResponse(account), StatusCodes.OK);
};

export const deleteAccount: AppRouteHandler<DeleteAccountRoute> = async (c) => {
  const user = c.get("user");
  const { id } = c.req.valid("param");

  await deleteAccountAction(id, user.id);
  return c.body(null, StatusCodes.NO_CONTENT);
};
