import type { AccountGroup, AccountGroupResponse, AccountResponse, AccountWithGroup } from "./types";

export const mapAccountGroupResponse = (accountGroup: AccountGroup): AccountGroupResponse => ({
  id: accountGroup.id,
  name: accountGroup.name,
  color: accountGroup.color,
  iconUrl: accountGroup.iconUrl,
  createdAt: accountGroup.createdAt.toISOString(),
  updatedAt: accountGroup.updatedAt.toISOString(),
});

export const mapAccountResponse = (account: AccountWithGroup): AccountResponse => ({
  id: account.id,
  groupId: account.groupId,
  group: account.group ? mapAccountGroupResponse(account.group) : null,
  name: account.name,
  currency: account.currency,
  type: account.type,
  description: account.description,
  color: account.color,
  overdraftLimit: account.overdraftLimit,
  isActive: account.isActive,
  openingBalance: account.openingBalance,
  currentBalance: account.currentBalance,
  baseOpeningBalance: account.baseOpeningBalance,
  baseCurrentBalance: account.baseCurrentBalance,
  createdAt: account.createdAt.toISOString(),
  updatedAt: account.updatedAt.toISOString(),
});
