import type {
  Account,
  AccountGroup,
  AccountGroupResponse,
  AccountResponse,
  AccountWithGroup,
  AccountWithGroupResponse,
} from "./types";

export const mapAccountGroupResponse = (accountGroup: AccountGroup): AccountGroupResponse => ({
  id: accountGroup.id,
  name: accountGroup.name,
  color: accountGroup.color,
  iconUrl: accountGroup.iconUrl,
  createdAt: accountGroup.createdAt.toISOString(),
  updatedAt: accountGroup.updatedAt.toISOString(),
});

export const mapAccountResponse = (account: Account): AccountResponse => ({
  id: account.id,
  groupId: account.groupId,
  name: account.name,
  currency: account.currency,
  type: account.type,
  description: account.description,
  color: account.color,
  overdraftLimit: account.overdraftLimit,
  isActive: account.isActive,
  openingBalance: account.openingBalance,
  currentBalance: account.currentBalance,
  baseOpeningBalance: account.baseOpeningBalance,
  baseCurrentBalance: account.baseCurrentBalance,
  createdAt: account.createdAt.toISOString(),
  updatedAt: account.updatedAt.toISOString(),
});

export const mapAccountWithGroupResponse = (accountWithGroup: AccountWithGroup): AccountWithGroupResponse => ({
  id: accountWithGroup.id,
  groupId: accountWithGroup.groupId,
  group: accountWithGroup.group ? mapAccountGroupResponse(accountWithGroup.group) : null,
  name: accountWithGroup.name,
  currency: accountWithGroup.currency,
  type: accountWithGroup.type,
  description: accountWithGroup.description,
  color: accountWithGroup.color,
  overdraftLimit: accountWithGroup.overdraftLimit,
  isActive: accountWithGroup.isActive,
  openingBalance: accountWithGroup.openingBalance,
  currentBalance: accountWithGroup.currentBalance,
  baseOpeningBalance: accountWithGroup.baseOpeningBalance,
  baseCurrentBalance: accountWithGroup.baseCurrentBalance,
  createdAt: accountWithGroup.createdAt.toISOString(),
  updatedAt: accountWithGroup.updatedAt.toISOString(),
});
