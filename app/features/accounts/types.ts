import type { accountGroupSelectSchema, accountSelectSchema } from "~/db/schemas";
import type {
  accountBalanceUpdateSchema,
  accountCreateSchema,
  accountGroupCreateSchema,
  accountGroupResponseSchema,
  accountGroupUpdateSchema,
  accountResponseSchema,
  accountUpdateSchema,
} from "./schemas";

import { z } from "@hono/zod-openapi";
import { z as z4 } from "zod/v4";

// Account Group Types
export type CreateAccountGroupData = z.infer<typeof accountGroupCreateSchema>;
export type UpdateAccountGroupData = z.infer<typeof accountGroupUpdateSchema>;
export type AccountGroup = z4.infer<typeof accountGroupSelectSchema>;
export type AccountGroupResponse = z.infer<typeof accountGroupResponseSchema>;

// Account Types
export type CreateAccountData = z.infer<typeof accountCreateSchema>;
export type UpdateAccountData = z.infer<typeof accountUpdateSchema>;
export type UpdateAccountBalanceData = z.infer<typeof accountBalanceUpdateSchema>;
export type Account = z4.infer<typeof accountSelectSchema>;
export type AccountResponse = z.infer<typeof accountResponseSchema>;

// Extended types for database queries
export type AccountWithGroup = Account & {
  group: AccountGroup | null;
};
