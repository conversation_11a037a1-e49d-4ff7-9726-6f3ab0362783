import type { RouteConfig, RouteHandler } from "@hono/zod-openapi";
import type { PinoLogger } from "hono-pino";
import type { JwtVariables } from "hono/jwt";
import type { User } from "./features/users/types";

export interface AppBindings {
  Variables: JwtVariables<{ sub: string }> & {
    logger: PinoLogger;
    user: User;
  };
}

export type AppRouteHandler<R extends RouteConfig> = RouteHandler<R, AppBindings>;
