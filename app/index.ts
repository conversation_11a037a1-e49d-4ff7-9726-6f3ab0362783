/* eslint-disable no-console */
import app from "./app";
import env from "./lib/env";
import { basePath as bullDashboardPath } from "./tasks/dashboard";

const port = env.PORT;

console.log(`Server is running on port http://localhost:${port}`);
console.log(`OpenAPI documentation is available at http://localhost:${port}/api/docs`);
console.log(`Bull dashboard is available at http://localhost:${port}${bullDashboardPath}`);

export default app;
